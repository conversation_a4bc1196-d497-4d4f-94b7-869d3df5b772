#!/usr/bin/env python3
"""
简化的视频接收程序 - 纯接收模式
专门用于AN5642等单向视频流开发板
"""

import socket
import time
import cv2
import numpy as np
import json

class SimpleVideoReceiver:
    def __init__(self, config_file=None):
        # 默认配置
        self.config = {
            'DEVELOP_BOARD_IP': '************',
            'DEVELOP_BOARD_PORT': 8080,
            'LOCAL_PORT': 8081,
            'BUFFER_SIZE': 65536,
            'TIMEOUT': 5
        }
        
        if config_file:
            self.load_config(config_file)
        
        self.socket = None
        self.running = False
        self.stats = {
            'packets_received': 0,
            'bytes_received': 0,
            'frames_decoded': 0,
            'start_time': None
        }
    
    def load_config(self, config_file):
        try:
            print(f"[DEBUG] 加载配置: {config_file}")
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                self.config.update(user_config)
            print(f"[DEBUG] 目标开发板: {self.config['DEVELOP_BOARD_IP']}:{self.config['DEVELOP_BOARD_PORT']}")
            print(f"[DEBUG] 本地监听端口: {self.config['LOCAL_PORT']}")
        except Exception as e:
            print(f"[ERROR] 配置加载失败: {e}")
    
    def setup_socket(self):
        try:
            print(f"[DEBUG] 创建UDP套接字...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.settimeout(self.config['TIMEOUT'])
            
            # 绑定到本地端口
            local_addr = ('', self.config['LOCAL_PORT'])
            self.socket.bind(local_addr)
            print(f"[DEBUG] 绑定成功: {local_addr}")
            
            return True
        except Exception as e:
            print(f"[ERROR] 套接字设置失败: {e}")
            return False
    
    def try_decode_frame(self, data):
        """尝试多种方式解码视频帧"""
        
        # 方法1: 直接解码JPEG
        try:
            img_array = np.frombuffer(data, dtype=np.uint8)
            frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            if frame is not None:
                print(f"[DEBUG] JPEG解码成功: {frame.shape}")
                return frame
        except Exception as e:
            print(f"[DEBUG] JPEG解码失败: {e}")
        
        # 方法2: 跳过协议头后解码
        for header_size in [0, 4, 8, 12, 16]:
            try:
                if len(data) > header_size:
                    img_data = data[header_size:]
                    img_array = np.frombuffer(img_data, dtype=np.uint8)
                    frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                    if frame is not None:
                        print(f"[DEBUG] 跳过{header_size}字节头部后JPEG解码成功: {frame.shape}")
                        return frame
            except:
                continue
        
        # 方法3: 尝试原始RGB数据
        try:
            # 常见分辨率
            resolutions = [(1280, 720), (640, 480), (1920, 1080), (800, 600)]
            for width, height in resolutions:
                expected_size = width * height * 3
                if len(data) == expected_size:
                    img_array = np.frombuffer(data, dtype=np.uint8)
                    frame = img_array.reshape((height, width, 3))
                    print(f"[DEBUG] 原始RGB解码成功: {width}x{height}")
                    return frame
        except Exception as e:
            print(f"[DEBUG] 原始RGB解码失败: {e}")
        
        return None
    
    def receive_video(self):
        """接收视频流"""
        if not self.setup_socket():
            return False
        
        print(f"[INFO] ========== 开始监听视频数据 ==========")
        print(f"[INFO] 监听端口: {self.config['LOCAL_PORT']}")
        print(f"[INFO] 等待来自 {self.config['DEVELOP_BOARD_IP']} 的视频数据...")
        print(f"[INFO] 按 'q' 退出，按 's' 截图")
        
        self.running = True
        self.stats['start_time'] = time.time()
        last_stats_time = time.time()
        
        try:
            while self.running:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(self.config['BUFFER_SIZE'])
                    
                    self.stats['packets_received'] += 1
                    self.stats['bytes_received'] += len(data)
                    
                    print(f"[DEBUG] 数据包 #{self.stats['packets_received']}: {len(data)}字节, 来源: {addr}")
                    
                    # 尝试解码
                    frame = self.try_decode_frame(data)
                    
                    if frame is not None:
                        self.stats['frames_decoded'] += 1
                        
                        # 显示视频
                        cv2.imshow("AN5642 Video Stream", frame)
                        
                        # 处理按键
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            print(f"[INFO] 用户退出")
                            break
                        elif key == ord('s'):
                            filename = f"screenshot_{int(time.time())}.jpg"
                            cv2.imwrite(filename, frame)
                            print(f"[INFO] 截图保存: {filename}")
                    
                    # 每30个数据包显示统计
                    if self.stats['packets_received'] % 30 == 0:
                        current_time = time.time()
                        elapsed = current_time - last_stats_time
                        pps = 30 / elapsed if elapsed > 0 else 0
                        
                        print(f"[STATS] 数据包: {self.stats['packets_received']}, "
                              f"解码帧: {self.stats['frames_decoded']}, "
                              f"速率: {pps:.1f} pps")
                        
                        last_stats_time = current_time
                
                except socket.timeout:
                    print(f"[INFO] 等待数据中... (已等待{self.config['TIMEOUT']}秒)")
                    continue
                    
                except Exception as e:
                    print(f"[ERROR] 接收错误: {e}")
                    break
        
        finally:
            self.cleanup()
        
        return True
    
    def cleanup(self):
        print(f"[INFO] 清理资源...")
        self.running = False
        
        if self.socket:
            self.socket.close()
        
        cv2.destroyAllWindows()
        
        # 显示最终统计
        if self.stats['start_time']:
            total_time = time.time() - self.stats['start_time']
            print(f"[INFO] ========== 最终统计 ==========")
            print(f"[INFO] 运行时间: {total_time:.1f}秒")
            print(f"[INFO] 收到数据包: {self.stats['packets_received']}")
            print(f"[INFO] 成功解码帧: {self.stats['frames_decoded']}")
            print(f"[INFO] 总数据量: {self.stats['bytes_received']/1024:.1f} KB")
        
        print(f"[INFO] 程序退出")

def main():
    print("========== AN5642 简化视频接收器 ==========")
    print("专门用于接收单向视频流")
    print()
    
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--config', help='配置文件路径')
    args = parser.parse_args()
    
    receiver = SimpleVideoReceiver(args.config)
    
    try:
        receiver.receive_video()
    except KeyboardInterrupt:
        print("\n[INFO] 用户中断")

if __name__ == "__main__":
    main()
