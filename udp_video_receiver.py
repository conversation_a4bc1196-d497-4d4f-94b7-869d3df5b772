import socket
import time
import struct
import json
import sys

# 检查依赖库
try:
    import numpy as np
    print("[DEBUG] numpy 导入成功")
except ImportError as e:
    print(f"[ERROR] numpy 导入失败: {e}")
    print("[ERROR] 请安装: pip install numpy")
    sys.exit(1)

try:
    import cv2
    print("[DEBUG] opencv-python 导入成功")
except ImportError as e:
    print(f"[ERROR] opencv-python 导入失败: {e}")
    print("[ERROR] 请安装: pip install opencv-python")
    sys.exit(1)

class VideoReceiver:
    def __init__(self, config_file=None):
        self.config = {
            'HEADER_QUERY': 0x00,
            'HEADER_RESPONSE': 0x01,
            'QUERY_FIXED': 0x00020001,
            'CONTROL_FIXED': 0x00020002,
            'TIMEOUT': 30,
            'DEVELOP_BOARD_IP': '************',
            'DEVELOP_BOARD_PORT': 8080,
            'LOCAL_IP': '',
            'LOCAL_PORT': 8080,
            'VIDEO_WIDTH': 1280,
            'VIDEO_HEIGHT': 720,
            'VIDEO_CHANNELS': 3,
            'BUFFER_SIZE': 65536,
            'PROTOCOL_HEADER_SIZE': 8,
            'MAC_ADDRESS': b'\xBC\xEC\xA0\x28\x8D\xA6',
            'CHANNEL': b'\x03'
        }

        if config_file:
            self.load_config(config_file)

        self.frame_size = (self.config['VIDEO_WIDTH'] *
                          self.config['VIDEO_HEIGHT'] *
                          self.config['VIDEO_CHANNELS'])

        self.stats = {
            'frames_received': 0,
            'bytes_received': 0,
            'start_time': None,
            'last_fps_time': None
        }

        self.socket = None
        self.running = False

    def load_config(self, config_file):
        try:
            print(f"[DEBUG] 加载配置文件: {config_file}")
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self.config.update(user_config)
            print(f"[DEBUG] 配置加载成功")
            print(f"[DEBUG] 开发板IP: {self.config['DEVELOP_BOARD_IP']}")
            print(f"[DEBUG] 开发板端口: {self.config['DEVELOP_BOARD_PORT']}")
            print(f"[DEBUG] 本地端口: {self.config['LOCAL_PORT']}")
        except Exception as e:
            print(f"[ERROR] 配置文件加载失败: {e}")
            pass

    def build_query_cmd(self):
        header = struct.pack('B', self.config['HEADER_QUERY'])
        fixed = struct.pack('>I', self.config['QUERY_FIXED'])
        cmd = header + fixed
        print(f"[DEBUG] 构建查询命令: header=0x{self.config['HEADER_QUERY']:02x}, fixed=0x{self.config['QUERY_FIXED']:08x}")
        print(f"[DEBUG] 查询命令字节: {cmd.hex()}")
        return cmd

    def build_control_cmd(self, start=True):
        header = struct.pack('B', self.config['HEADER_QUERY'])
        fixed = struct.pack('>I', self.config['CONTROL_FIXED'])
        mac = self.config['MAC_ADDRESS']
        channel = self.config['CHANNEL']
        signal = b'\x01' if start else b'\x00'
        cmd = header + fixed + mac + channel + signal

        action = "启动" if start else "停止"
        print(f"[DEBUG] 构建{action}控制命令:")
        print(f"[DEBUG]   header=0x{self.config['HEADER_QUERY']:02x}")
        print(f"[DEBUG]   fixed=0x{self.config['CONTROL_FIXED']:08x}")
        print(f"[DEBUG]   mac={mac.hex() if isinstance(mac, bytes) else mac}")
        print(f"[DEBUG]   channel={channel.hex() if isinstance(channel, bytes) else channel}")
        print(f"[DEBUG]   signal=0x{signal[0]:02x}")
        print(f"[DEBUG] 控制命令字节: {cmd.hex()}")

        return cmd

    def verify_response_header(self, header_byte):
        is_valid = (header_byte & 0x01) == self.config['HEADER_RESPONSE']
        print(f"[DEBUG] 验证响应头: 0x{header_byte:02x}, 期望: 0x{self.config['HEADER_RESPONSE']:02x}, 有效: {is_valid}")
        return is_valid

    def setup_socket(self):
        try:
            print(f"[DEBUG] 创建UDP套接字...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.settimeout(self.config['TIMEOUT'])
            local_address = (self.config['LOCAL_IP'], self.config['LOCAL_PORT'])
            print(f"[DEBUG] 绑定本地地址: {local_address}")
            self.socket.bind(local_address)
            print(f"[DEBUG] 套接字设置成功，超时时间: {self.config['TIMEOUT']}秒")
            return True
        except Exception as e:
            print(f"[ERROR] 套接字设置失败: {e}")
            return False

    def send_query_and_wait_response(self):
        try:
            query_cmd = self.build_query_cmd()
            target_addr = (self.config['DEVELOP_BOARD_IP'], self.config['DEVELOP_BOARD_PORT'])
            print(f"[DEBUG] 发送查询命令到: {target_addr}")
            print(f"[DEBUG] 查询命令内容: {query_cmd.hex()}")

            self.socket.sendto(query_cmd, target_addr)
            print(f"[DEBUG] 等待开发板响应...")

            resp_data, addr = self.socket.recvfrom(4096)
            print(f"[DEBUG] 收到响应，来源: {addr}")
            print(f"[DEBUG] 响应数据长度: {len(resp_data)} 字节")
            print(f"[DEBUG] 响应数据: {resp_data.hex()}")

            if addr[0] != self.config['DEVELOP_BOARD_IP']:
                print(f"[WARNING] 响应来源IP不匹配，期望: {self.config['DEVELOP_BOARD_IP']}, 实际: {addr[0]}")
                return False

            if len(resp_data) < 1:
                print(f"[ERROR] 响应数据为空")
                return False

            print(f"[DEBUG] 查询响应验证成功")
            return True

        except socket.timeout:
            print(f"[ERROR] 查询超时，开发板可能未响应")
            return False
        except Exception as e:
            print(f"[ERROR] 查询失败: {e}")
            return False

    def start_video_stream(self):
        try:
            control_cmd = self.build_control_cmd(start=True)
            target_addr = (self.config['DEVELOP_BOARD_IP'], self.config['DEVELOP_BOARD_PORT'])
            print(f"[DEBUG] 发送视频流启动命令到: {target_addr}")
            print(f"[DEBUG] 启动命令内容: {control_cmd.hex()}")

            self.socket.sendto(control_cmd, target_addr)
            print(f"[DEBUG] 视频流启动命令已发送")
            return True
        except Exception as e:
            print(f"[ERROR] 发送视频流启动命令失败: {e}")
            return False

    def stop_video_stream(self):
        try:
            stop_cmd = self.build_control_cmd(start=False)
            self.socket.sendto(stop_cmd,
                             (self.config['DEVELOP_BOARD_IP'],
                              self.config['DEVELOP_BOARD_PORT']))
            return True
        except:
            return False

    def process_frame_data(self, data):
        try:
            print(f"[DEBUG] 处理帧数据: 总长度={len(data)} 字节")

            header_size = self.config['PROTOCOL_HEADER_SIZE']
            print(f"[DEBUG] 协议头大小: {header_size} 字节")

            if len(data) > header_size:
                # 解析协议头
                header_data = data[:header_size]
                img_data = data[header_size:]
                print(f"[DEBUG] 协议头: {header_data.hex()}")
                print(f"[DEBUG] 图像数据长度: {len(img_data)} 字节")

                # 尝试解析协议头信息
                if len(header_data) >= 5:
                    header_byte = header_data[0]
                    frame_info = struct.unpack('>I', header_data[1:5])[0]
                    print(f"[DEBUG] 头字节: 0x{header_byte:02x}, 帧信息: 0x{frame_info:08x}")
            else:
                img_data = data
                print(f"[DEBUG] 数据太短，直接作为图像数据处理")

            img_array = np.frombuffer(img_data, dtype=np.uint8)
            print(f"[DEBUG] 图像数组大小: {img_array.size} 字节")
            frame = None

            # 方法1: 尝试作为原始RGB数据解码
            if img_array.size == self.frame_size:
                try:
                    frame = img_array.reshape((
                        self.config['VIDEO_HEIGHT'],
                        self.config['VIDEO_WIDTH'],
                        self.config['VIDEO_CHANNELS']
                    ))
                    print(f"[DEBUG] 原始RGB解码成功: {frame.shape}")
                except Exception as e:
                    print(f"[DEBUG] 原始RGB解码失败: {e}")

            # 方法2: 尝试作为压缩图像解码
            if frame is None:
                try:
                    frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                    if frame is not None:
                        print(f"[DEBUG] 压缩图像解码成功: {frame.shape}")
                    else:
                        print(f"[DEBUG] 压缩图像解码失败: cv2.imdecode返回None")
                except Exception as e:
                    print(f"[DEBUG] 压缩图像解码异常: {e}")

            if frame is None:
                print(f"[WARNING] 帧解码完全失败")
                # 显示数据的前几个字节用于调试
                preview = img_data[:min(32, len(img_data))]
                print(f"[DEBUG] 数据预览: {preview.hex()}")

            return frame
        except Exception as e:
            print(f"[ERROR] 处理帧数据时发生异常: {e}")
            import traceback
            traceback.print_exc()
            return None

    def update_statistics(self, data_size):
        self.stats['frames_received'] += 1
        self.stats['bytes_received'] += data_size

        current_time = time.time()

        # 每10帧显示一次详细统计
        if self.stats['frames_received'] % 10 == 0:
            if self.stats['start_time'] is not None:
                total_elapsed = current_time - self.stats['start_time']
                avg_fps = self.stats['frames_received'] / total_elapsed if total_elapsed > 0 else 0
                avg_bytes_per_frame = self.stats['bytes_received'] / self.stats['frames_received']

                print(f"[STATS] 帧数: {self.stats['frames_received']}, "
                      f"平均FPS: {avg_fps:.2f}, "
                      f"平均帧大小: {avg_bytes_per_frame:.0f} 字节")

        # 每30帧计算瞬时帧率
        if self.stats['frames_received'] % 30 == 0:
            if self.stats['last_fps_time'] is not None:
                elapsed = current_time - self.stats['last_fps_time']
                fps = 30 / elapsed if elapsed > 0 else 0
                print(f"[STATS] 瞬时帧率: {fps:.2f} FPS | 总帧数: {self.stats['frames_received']}")
                print(f"[STATS] 总接收数据: {self.stats['bytes_received']/1024/1024:.2f} MB")

            self.stats['last_fps_time'] = current_time

    def receive_video_stream(self):
        print(f"[DEBUG] ========== 开始视频接收流程 ==========")
        print(f"[DEBUG] 目标开发板: {self.config['DEVELOP_BOARD_IP']}:{self.config['DEVELOP_BOARD_PORT']}")

        if not self.setup_socket():
            print(f"[ERROR] 套接字设置失败")
            return False

        try:
            print(f"[DEBUG] ========== 第1步: 发送查询命令 ==========")
            if not self.send_query_and_wait_response():
                print(f"[ERROR] 查询命令失败")
                return False

            print(f"[DEBUG] ========== 第2步: 启动视频流 ==========")
            if not self.start_video_stream():
                print(f"[ERROR] 视频流启动失败")
                return False

            print(f"[DEBUG] ========== 第3步: 开始接收视频数据 ==========")
            print("开始接收视频流... 按 'q' 键退出，按 's' 键截图")

            self.running = True
            self.stats['start_time'] = time.time()
            self.stats['last_fps_time'] = time.time()

            control_cmd = self.build_control_cmd(start=True)
            last_keepalive = time.time()

            frame_count = 0
            last_data_time = time.time()

            while self.running:
                try:
                    print(f"[DEBUG] 等待接收数据包...")
                    data, addr = self.socket.recvfrom(self.config['BUFFER_SIZE'])
                    frame_count += 1
                    current_time = time.time()

                    print(f"[DEBUG] ========== 收到数据包 #{frame_count} ==========")
                    print(f"[DEBUG] 来源地址: {addr}")
                    print(f"[DEBUG] 数据大小: {len(data)} 字节")
                    print(f"[DEBUG] 接收间隔: {current_time - last_data_time:.3f} 秒")
                    last_data_time = current_time

                    if addr[0] != self.config['DEVELOP_BOARD_IP']:
                        print(f"[WARNING] 收到来自未知IP的数据: {addr[0]}, 期望: {self.config['DEVELOP_BOARD_IP']}")
                        continue

                    # 显示数据的前几个字节
                    preview_bytes = min(16, len(data))
                    preview = data[:preview_bytes].hex()
                    print(f"[DEBUG] 数据预览 (前{preview_bytes}字节): {preview}")

                    self.update_statistics(len(data))

                    print(f"[DEBUG] 开始处理帧数据...")
                    frame = self.process_frame_data(data)

                    if frame is not None:
                        print(f"[DEBUG] 帧处理成功! 尺寸: {frame.shape}, 类型: {frame.dtype}")

                        # 显示图像窗口
                        cv2.imshow("Video Stream", frame)
                        print(f"[DEBUG] 图像已显示在窗口中")

                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            print(f"[DEBUG] 用户按下 'q' 键，退出程序")
                            break
                        elif key == ord('s'):
                            filename = f"screenshot_{int(time.time())}.jpg"
                            success = cv2.imwrite(filename, frame)
                            if success:
                                print(f"[DEBUG] 截图已保存: {filename}")
                            else:
                                print(f"[ERROR] 截图保存失败: {filename}")
                    else:
                        print(f"[ERROR] 第{frame_count}帧处理失败!")

                    # 发送保活命令
                    if current_time - last_keepalive > 1.0:
                        print(f"[DEBUG] 发送保活命令...")
                        self.socket.sendto(control_cmd,
                                         (self.config['DEVELOP_BOARD_IP'],
                                          self.config['DEVELOP_BOARD_PORT']))
                        last_keepalive = current_time

                except socket.timeout:
                    timeout_duration = time.time() - last_data_time
                    print(f"[WARNING] 接收超时 ({timeout_duration:.1f}秒无数据)，发送保活命令...")
                    try:
                        self.socket.sendto(control_cmd,
                                         (self.config['DEVELOP_BOARD_IP'],
                                          self.config['DEVELOP_BOARD_PORT']))
                        print(f"[DEBUG] 保活命令已发送")
                    except Exception as e:
                        print(f"[ERROR] 发送保活命令失败: {e}")

                except Exception as e:
                    print(f"[ERROR] 接收数据时发生错误: {e}")
                    import traceback
                    traceback.print_exc()
                    break

            return True

        except:
            return False

        finally:
            self.cleanup()

    def cleanup(self):
        print(f"[DEBUG] ========== 开始清理资源 ==========")
        self.running = False
        if self.socket:
            print(f"[DEBUG] 发送停止视频流命令...")
            self.stop_video_stream()
            print(f"[DEBUG] 关闭套接字...")
            self.socket.close()
        cv2.destroyAllWindows()

        # 显示统计信息
        if self.stats['start_time']:
            total_time = time.time() - self.stats['start_time']
            avg_fps = self.stats['frames_received'] / total_time if total_time > 0 else 0
            print(f"[INFO] ========== 运行统计 ==========")
            print(f"[INFO] 总运行时间: {total_time:.2f} 秒")
            print(f"[INFO] 总接收帧数: {self.stats['frames_received']}")
            print(f"[INFO] 总接收字节: {self.stats['bytes_received']}")
            print(f"[INFO] 平均帧率: {avg_fps:.2f} FPS")

        print("[INFO] 程序已退出")


def test_network_connection(ip, port):
    """测试网络连接"""
    print(f"[DEBUG] ========== 网络连接测试 ==========")
    print(f"[DEBUG] 测试目标: {ip}:{port}")

    # 1. 测试ping连通性
    import subprocess
    try:
        print(f"[DEBUG] 测试ping连通性...")
        result = subprocess.run(['ping', '-n', '1', ip],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"[DEBUG] Ping成功: {ip} 可达")
        else:
            print(f"[WARNING] Ping失败: {ip} 不可达")
            print(f"[DEBUG] Ping输出: {result.stdout}")
    except Exception as e:
        print(f"[WARNING] Ping测试失败: {e}")

    # 2. 测试UDP通信
    try:
        # 创建测试套接字
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        test_socket.settimeout(5)

        # 发送简单的测试数据
        test_data = b"TEST"
        test_socket.sendto(test_data, (ip, port))
        print(f"[DEBUG] 测试数据已发送")

        # 尝试接收响应
        try:
            data, addr = test_socket.recvfrom(1024)
            print(f"[DEBUG] 收到响应: {len(data)} 字节，来源: {addr}")
            return True
        except socket.timeout:
            print(f"[WARNING] UDP测试超时，开发板可能没有在监听端口{port}")
            return False

    except Exception as e:
        print(f"[ERROR] UDP测试失败: {e}")
        return False
    finally:
        test_socket.close()


def main():
    print("[DEBUG] ========== 程序启动 ==========")

    try:
        import argparse
        parser = argparse.ArgumentParser()
        parser.add_argument('-c', '--config', help='配置文件路径')
        parser.add_argument('--test-network', action='store_true', help='测试网络连接')
        parser.add_argument('--debug', action='store_true', help='显示详细调试信息')
        args = parser.parse_args()

        print(f"[DEBUG] 命令行参数解析成功")
        print(f"[DEBUG] 配置文件: {args.config}")

        print("[DEBUG] 创建VideoReceiver实例...")
        receiver = VideoReceiver(args.config)
        print("[DEBUG] VideoReceiver实例创建成功")

        # 总是先进行网络测试
        print("[DEBUG] 执行网络连接测试...")
        network_ok = test_network_connection(receiver.config['DEVELOP_BOARD_IP'],
                                           receiver.config['DEVELOP_BOARD_PORT'])

        if not network_ok:
            print("[ERROR] 网络连接测试失败，请检查:")
            print("  1. 开发板是否已启动")
            print("  2. 网络连接是否正常")
            print("  3. IP地址是否正确")
            print("  4. 开发板上的视频服务是否运行")
            return

        print("[DEBUG] 开始视频接收...")
        receiver.receive_video_stream()

    except KeyboardInterrupt:
        print("[INFO] 用户中断程序")
    except Exception as e:
        print(f"[ERROR] 程序运行时发生未处理的异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("[DEBUG] 程序结束")


if __name__ == "__main__":
    main()
