import socket
import time
import struct
import json
import sys

# 检查依赖库
try:
    import numpy as np
    print("[DEBUG] numpy 导入成功")
except ImportError as e:
    print(f"[ERROR] numpy 导入失败: {e}")
    print("[ERROR] 请安装: pip install numpy")
    sys.exit(1)

try:
    import cv2
    print("[DEBUG] opencv-python 导入成功")
except ImportError as e:
    print(f"[ERROR] opencv-python 导入失败: {e}")
    print("[ERROR] 请安装: pip install opencv-python")
    sys.exit(1)

class VideoReceiver:
    def __init__(self, config_file=None):
        self.config = {
            'HEADER_QUERY': 0x00,
            'HEADER_RESPONSE': 0x01,
            'QUERY_FIXED': 0x00020001,
            'CONTROL_FIXED': 0x00020002,
            'TIMEOUT': 30,
            'DEVELOP_BOARD_IP': '************',
            'DEVELOP_BOARD_PORT': 8080,
            'LOCAL_IP': '',
            'LOCAL_PORT': 8080,
            'VIDEO_WIDTH': 1280,
            'VIDEO_HEIGHT': 720,
            'VIDEO_CHANNELS': 3,
            'BUFFER_SIZE': 65536,
            'PROTOCOL_HEADER_SIZE': 8,
            'MAC_ADDRESS': b'\xBC\xEC\xA0\x28\x8D\xA6',
            'CHANNEL': b'\x03'
        }

        if config_file:
            self.load_config(config_file)

        self.frame_size = (self.config['VIDEO_WIDTH'] *
                          self.config['VIDEO_HEIGHT'] *
                          self.config['VIDEO_CHANNELS'])

        self.stats = {
            'frames_received': 0,
            'bytes_received': 0,
            'start_time': None,
            'last_fps_time': None
        }

        self.socket = None
        self.running = False

    def load_config(self, config_file):
        try:
            print(f"[DEBUG] 加载配置文件: {config_file}")
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self.config.update(user_config)
            print(f"[DEBUG] 配置加载成功")
            print(f"[DEBUG] 开发板IP: {self.config['DEVELOP_BOARD_IP']}")
            print(f"[DEBUG] 开发板端口: {self.config['DEVELOP_BOARD_PORT']}")
            print(f"[DEBUG] 本地端口: {self.config['LOCAL_PORT']}")
        except Exception as e:
            print(f"[ERROR] 配置文件加载失败: {e}")
            pass

    def build_query_cmd(self):
        header = struct.pack('B', self.config['HEADER_QUERY'])
        fixed = struct.pack('>I', self.config['QUERY_FIXED'])
        return header + fixed

    def build_control_cmd(self, start=True):
        header = struct.pack('B', self.config['HEADER_QUERY'])
        fixed = struct.pack('>I', self.config['CONTROL_FIXED'])
        mac = self.config['MAC_ADDRESS']
        channel = self.config['CHANNEL']
        signal = b'\x01' if start else b'\x00'
        return header + fixed + mac + channel + signal

    def verify_response_header(self, header_byte):
        return (header_byte & 0x01) == self.config['HEADER_RESPONSE']

    def setup_socket(self):
        try:
            print(f"[DEBUG] 创建UDP套接字...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.settimeout(self.config['TIMEOUT'])
            local_address = (self.config['LOCAL_IP'], self.config['LOCAL_PORT'])
            print(f"[DEBUG] 绑定本地地址: {local_address}")
            self.socket.bind(local_address)
            print(f"[DEBUG] 套接字设置成功，超时时间: {self.config['TIMEOUT']}秒")
            return True
        except Exception as e:
            print(f"[ERROR] 套接字设置失败: {e}")
            return False

    def send_query_and_wait_response(self):
        try:
            query_cmd = self.build_query_cmd()
            target_addr = (self.config['DEVELOP_BOARD_IP'], self.config['DEVELOP_BOARD_PORT'])
            print(f"[DEBUG] 发送查询命令到: {target_addr}")
            print(f"[DEBUG] 查询命令内容: {query_cmd.hex()}")

            self.socket.sendto(query_cmd, target_addr)
            print(f"[DEBUG] 等待开发板响应...")

            resp_data, addr = self.socket.recvfrom(4096)
            print(f"[DEBUG] 收到响应，来源: {addr}")
            print(f"[DEBUG] 响应数据长度: {len(resp_data)} 字节")
            print(f"[DEBUG] 响应数据: {resp_data.hex()}")

            if addr[0] != self.config['DEVELOP_BOARD_IP']:
                print(f"[WARNING] 响应来源IP不匹配，期望: {self.config['DEVELOP_BOARD_IP']}, 实际: {addr[0]}")
                return False

            if len(resp_data) < 1:
                print(f"[ERROR] 响应数据为空")
                return False

            print(f"[DEBUG] 查询响应验证成功")
            return True

        except socket.timeout:
            print(f"[ERROR] 查询超时，开发板可能未响应")
            return False
        except Exception as e:
            print(f"[ERROR] 查询失败: {e}")
            return False

    def start_video_stream(self):
        try:
            control_cmd = self.build_control_cmd(start=True)
            target_addr = (self.config['DEVELOP_BOARD_IP'], self.config['DEVELOP_BOARD_PORT'])
            print(f"[DEBUG] 发送视频流启动命令到: {target_addr}")
            print(f"[DEBUG] 启动命令内容: {control_cmd.hex()}")

            self.socket.sendto(control_cmd, target_addr)
            print(f"[DEBUG] 视频流启动命令已发送")
            return True
        except Exception as e:
            print(f"[ERROR] 发送视频流启动命令失败: {e}")
            return False

    def stop_video_stream(self):
        try:
            stop_cmd = self.build_control_cmd(start=False)
            self.socket.sendto(stop_cmd,
                             (self.config['DEVELOP_BOARD_IP'],
                              self.config['DEVELOP_BOARD_PORT']))
            return True
        except:
            return False

    def process_frame_data(self, data):
        try:
            header_size = self.config['PROTOCOL_HEADER_SIZE']
            if len(data) > header_size:
                img_data = data[header_size:]
            else:
                img_data = data

            img_array = np.frombuffer(img_data, dtype=np.uint8)
            frame = None

            if img_array.size == self.frame_size:
                try:
                    frame = img_array.reshape((
                        self.config['VIDEO_HEIGHT'],
                        self.config['VIDEO_WIDTH'],
                        self.config['VIDEO_CHANNELS']
                    ))
                except:
                    pass

            if frame is None:
                try:
                    frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                except:
                    pass

            return frame
        except:
            return None

    def update_statistics(self, data_size):
        self.stats['frames_received'] += 1
        self.stats['bytes_received'] += data_size

        current_time = time.time()

        if self.stats['frames_received'] % 30 == 0:
            if self.stats['last_fps_time'] is not None:
                elapsed = current_time - self.stats['last_fps_time']
                fps = 30 / elapsed if elapsed > 0 else 0
                print(f"帧率: {fps:.2f} FPS | 总帧数: {self.stats['frames_received']}")

            self.stats['last_fps_time'] = current_time

    def receive_video_stream(self):
        print(f"[DEBUG] ========== 开始视频接收流程 ==========")
        print(f"[DEBUG] 目标开发板: {self.config['DEVELOP_BOARD_IP']}:{self.config['DEVELOP_BOARD_PORT']}")

        if not self.setup_socket():
            print(f"[ERROR] 套接字设置失败")
            return False

        try:
            print(f"[DEBUG] ========== 第1步: 发送查询命令 ==========")
            if not self.send_query_and_wait_response():
                print(f"[ERROR] 查询命令失败")
                return False

            print(f"[DEBUG] ========== 第2步: 启动视频流 ==========")
            if not self.start_video_stream():
                print(f"[ERROR] 视频流启动失败")
                return False

            print(f"[DEBUG] ========== 第3步: 开始接收视频数据 ==========")
            print("开始接收视频流... 按 'q' 键退出，按 's' 键截图")

            self.running = True
            self.stats['start_time'] = time.time()
            self.stats['last_fps_time'] = time.time()

            control_cmd = self.build_control_cmd(start=True)
            last_keepalive = time.time()

            frame_count = 0
            while self.running:
                try:
                    data, addr = self.socket.recvfrom(self.config['BUFFER_SIZE'])
                    frame_count += 1

                    if addr[0] != self.config['DEVELOP_BOARD_IP']:
                        print(f"[WARNING] 收到来自未知IP的数据: {addr[0]}")
                        continue

                    # 每100帧显示一次详细信息
                    if frame_count % 100 == 1:
                        print(f"[DEBUG] 收到第{frame_count}帧数据，大小: {len(data)} 字节，来源: {addr}")

                    self.update_statistics(len(data))

                    frame = self.process_frame_data(data)
                    if frame is not None:
                        cv2.imshow("Video Stream", frame)

                        # 每100帧显示一次成功信息
                        if frame_count % 100 == 1:
                            print(f"[DEBUG] 帧解码成功，尺寸: {frame.shape}")

                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            print(f"[DEBUG] 用户按下 'q' 键，退出程序")
                            break
                        elif key == ord('s'):
                            filename = f"screenshot_{int(time.time())}.jpg"
                            cv2.imwrite(filename, frame)
                            print(f"[DEBUG] 截图已保存: {filename}")
                    else:
                        # 只在前几次失败时显示错误
                        if frame_count <= 10:
                            print(f"[WARNING] 第{frame_count}帧解码失败，数据大小: {len(data)}")

                    current_time = time.time()
                    if current_time - last_keepalive > 1.0:
                        self.socket.sendto(control_cmd,
                                         (self.config['DEVELOP_BOARD_IP'],
                                          self.config['DEVELOP_BOARD_PORT']))
                        last_keepalive = current_time

                except socket.timeout:
                    print(f"[DEBUG] 接收超时，发送保活命令...")
                    self.socket.sendto(control_cmd,
                                     (self.config['DEVELOP_BOARD_IP'],
                                      self.config['DEVELOP_BOARD_PORT']))
                except Exception as e:
                    print(f"[ERROR] 接收数据时发生错误: {e}")
                    break

            return True

        except:
            return False

        finally:
            self.cleanup()

    def cleanup(self):
        print(f"[DEBUG] ========== 开始清理资源 ==========")
        self.running = False
        if self.socket:
            print(f"[DEBUG] 发送停止视频流命令...")
            self.stop_video_stream()
            print(f"[DEBUG] 关闭套接字...")
            self.socket.close()
        cv2.destroyAllWindows()

        # 显示统计信息
        if self.stats['start_time']:
            total_time = time.time() - self.stats['start_time']
            avg_fps = self.stats['frames_received'] / total_time if total_time > 0 else 0
            print(f"[INFO] ========== 运行统计 ==========")
            print(f"[INFO] 总运行时间: {total_time:.2f} 秒")
            print(f"[INFO] 总接收帧数: {self.stats['frames_received']}")
            print(f"[INFO] 总接收字节: {self.stats['bytes_received']}")
            print(f"[INFO] 平均帧率: {avg_fps:.2f} FPS")

        print("[INFO] 程序已退出")


def test_network_connection(ip, port):
    """测试网络连接"""
    print(f"[DEBUG] ========== 网络连接测试 ==========")
    print(f"[DEBUG] 测试目标: {ip}:{port}")

    # 1. 测试ping连通性
    import subprocess
    try:
        print(f"[DEBUG] 测试ping连通性...")
        result = subprocess.run(['ping', '-n', '1', ip],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"[DEBUG] Ping成功: {ip} 可达")
        else:
            print(f"[WARNING] Ping失败: {ip} 不可达")
            print(f"[DEBUG] Ping输出: {result.stdout}")
    except Exception as e:
        print(f"[WARNING] Ping测试失败: {e}")

    # 2. 测试UDP通信
    try:
        # 创建测试套接字
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        test_socket.settimeout(5)

        # 发送简单的测试数据
        test_data = b"TEST"
        test_socket.sendto(test_data, (ip, port))
        print(f"[DEBUG] 测试数据已发送")

        # 尝试接收响应
        try:
            data, addr = test_socket.recvfrom(1024)
            print(f"[DEBUG] 收到响应: {len(data)} 字节，来源: {addr}")
            return True
        except socket.timeout:
            print(f"[WARNING] UDP测试超时，开发板可能没有在监听端口{port}")
            return False

    except Exception as e:
        print(f"[ERROR] UDP测试失败: {e}")
        return False
    finally:
        test_socket.close()


def main():
    print("[DEBUG] ========== 程序启动 ==========")

    try:
        import argparse
        parser = argparse.ArgumentParser()
        parser.add_argument('-c', '--config', help='配置文件路径')
        parser.add_argument('--test-network', action='store_true', help='测试网络连接')
        parser.add_argument('--debug', action='store_true', help='显示详细调试信息')
        args = parser.parse_args()

        print(f"[DEBUG] 命令行参数解析成功")
        print(f"[DEBUG] 配置文件: {args.config}")

        print("[DEBUG] 创建VideoReceiver实例...")
        receiver = VideoReceiver(args.config)
        print("[DEBUG] VideoReceiver实例创建成功")

        # 总是先进行网络测试
        print("[DEBUG] 执行网络连接测试...")
        network_ok = test_network_connection(receiver.config['DEVELOP_BOARD_IP'],
                                           receiver.config['DEVELOP_BOARD_PORT'])

        if not network_ok:
            print("[ERROR] 网络连接测试失败，请检查:")
            print("  1. 开发板是否已启动")
            print("  2. 网络连接是否正常")
            print("  3. IP地址是否正确")
            print("  4. 开发板上的视频服务是否运行")
            return

        print("[DEBUG] 开始视频接收...")
        receiver.receive_video_stream()

    except KeyboardInterrupt:
        print("[INFO] 用户中断程序")
    except Exception as e:
        print(f"[ERROR] 程序运行时发生未处理的异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("[DEBUG] 程序结束")


if __name__ == "__main__":
    main()
