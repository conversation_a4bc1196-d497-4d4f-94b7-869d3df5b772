import socket
import numpy as np
import cv2
import time
import struct
import json

class VideoReceiver:
    def __init__(self, config_file=None):
        self.config = {
            'HEADER_QUERY': 0x00,
            'HEADER_RESPONSE': 0x01,
            'QUERY_FIXED': 0x00020001,
            'CONTROL_FIXED': 0x00020002,
            'TIMEOUT': 30,
            'DEVELOP_BOARD_IP': '************',
            'DEVELOP_BOARD_PORT': 8080,
            'LOCAL_IP': '',
            'LOCAL_PORT': 8080,
            'VIDEO_WIDTH': 1280,
            'VIDEO_HEIGHT': 720,
            'VIDEO_CHANNELS': 3,
            'BUFFER_SIZE': 65536,
            'PROTOCOL_HEADER_SIZE': 8,
            'MAC_ADDRESS': b'\xBC\xEC\xA0\x28\x8D\xA6',
            'CHANNEL': b'\x03'
        }

        if config_file:
            self.load_config(config_file)

        self.frame_size = (self.config['VIDEO_WIDTH'] *
                          self.config['VIDEO_HEIGHT'] *
                          self.config['VIDEO_CHANNELS'])

        self.stats = {
            'frames_received': 0,
            'bytes_received': 0,
            'start_time': None,
            'last_fps_time': None
        }

        self.socket = None
        self.running = False

    def load_config(self, config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self.config.update(user_config)
        except:
            pass

    def build_query_cmd(self):
        header = struct.pack('B', self.config['HEADER_QUERY'])
        fixed = struct.pack('>I', self.config['QUERY_FIXED'])
        return header + fixed

    def build_control_cmd(self, start=True):
        header = struct.pack('B', self.config['HEADER_QUERY'])
        fixed = struct.pack('>I', self.config['CONTROL_FIXED'])
        mac = self.config['MAC_ADDRESS']
        channel = self.config['CHANNEL']
        signal = b'\x01' if start else b'\x00'
        return header + fixed + mac + channel + signal

    def verify_response_header(self, header_byte):
        return (header_byte & 0x01) == self.config['HEADER_RESPONSE']

    def setup_socket(self):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.settimeout(self.config['TIMEOUT'])
            local_address = (self.config['LOCAL_IP'], self.config['LOCAL_PORT'])
            self.socket.bind(local_address)
            return True
        except:
            return False

    def send_query_and_wait_response(self):
        try:
            query_cmd = self.build_query_cmd()
            self.socket.sendto(query_cmd,
                             (self.config['DEVELOP_BOARD_IP'],
                              self.config['DEVELOP_BOARD_PORT']))

            resp_data, addr = self.socket.recvfrom(4096)

            if addr[0] != self.config['DEVELOP_BOARD_IP']:
                return False

            if len(resp_data) < 1:
                return False

            return True

        except:
            return False

    def start_video_stream(self):
        try:
            control_cmd = self.build_control_cmd(start=True)
            self.socket.sendto(control_cmd,
                             (self.config['DEVELOP_BOARD_IP'],
                              self.config['DEVELOP_BOARD_PORT']))
            return True
        except:
            return False

    def stop_video_stream(self):
        try:
            stop_cmd = self.build_control_cmd(start=False)
            self.socket.sendto(stop_cmd,
                             (self.config['DEVELOP_BOARD_IP'],
                              self.config['DEVELOP_BOARD_PORT']))
            return True
        except:
            return False

    def process_frame_data(self, data):
        try:
            header_size = self.config['PROTOCOL_HEADER_SIZE']
            if len(data) > header_size:
                img_data = data[header_size:]
            else:
                img_data = data

            img_array = np.frombuffer(img_data, dtype=np.uint8)
            frame = None

            if img_array.size == self.frame_size:
                try:
                    frame = img_array.reshape((
                        self.config['VIDEO_HEIGHT'],
                        self.config['VIDEO_WIDTH'],
                        self.config['VIDEO_CHANNELS']
                    ))
                except:
                    pass

            if frame is None:
                try:
                    frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                except:
                    pass

            return frame
        except:
            return None

    def update_statistics(self, data_size):
        self.stats['frames_received'] += 1
        self.stats['bytes_received'] += data_size

        current_time = time.time()

        if self.stats['frames_received'] % 30 == 0:
            if self.stats['last_fps_time'] is not None:
                elapsed = current_time - self.stats['last_fps_time']
                fps = 30 / elapsed if elapsed > 0 else 0
                print(f"帧率: {fps:.2f} FPS | 总帧数: {self.stats['frames_received']}")

            self.stats['last_fps_time'] = current_time

    def receive_video_stream(self):
        if not self.setup_socket():
            return False

        try:
            if not self.send_query_and_wait_response():
                return False

            if not self.start_video_stream():
                return False

            print("开始接收视频流... 按 'q' 键退出，按 's' 键截图")

            self.running = True
            self.stats['start_time'] = time.time()
            self.stats['last_fps_time'] = time.time()

            control_cmd = self.build_control_cmd(start=True)
            last_keepalive = time.time()

            while self.running:
                try:
                    data, addr = self.socket.recvfrom(self.config['BUFFER_SIZE'])

                    if addr[0] != self.config['DEVELOP_BOARD_IP']:
                        continue

                    self.update_statistics(len(data))

                    frame = self.process_frame_data(data)
                    if frame is not None:
                        cv2.imshow("Video Stream", frame)

                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            break
                        elif key == ord('s'):
                            cv2.imwrite(f"screenshot_{int(time.time())}.jpg", frame)
                            print("截图已保存")

                    current_time = time.time()
                    if current_time - last_keepalive > 1.0:
                        self.socket.sendto(control_cmd,
                                         (self.config['DEVELOP_BOARD_IP'],
                                          self.config['DEVELOP_BOARD_PORT']))
                        last_keepalive = current_time

                except socket.timeout:
                    self.socket.sendto(control_cmd,
                                     (self.config['DEVELOP_BOARD_IP'],
                                      self.config['DEVELOP_BOARD_PORT']))
                except:
                    break

            return True

        except:
            return False

        finally:
            self.cleanup()

    def cleanup(self):
        self.running = False
        if self.socket:
            self.stop_video_stream()
            self.socket.close()
        cv2.destroyAllWindows()
        print("程序已退出")


def main():
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--config', help='配置文件路径')
    args = parser.parse_args()

    receiver = VideoReceiver(args.config)

    try:
        receiver.receive_video_stream()
    except KeyboardInterrupt:
        print("用户中断程序")


if __name__ == "__main__":
    main()
