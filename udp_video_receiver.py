import socket
import numpy as np
import cv2
import time
import struct

# 协议配置 - 根据开发板实际协议调整
HEADER_QUERY = 0x00  # 查询命令Header标识
HEADER_RESPONSE = 0x01  # 应答命令Header标识
QUERY_FIXED = 0x00020001  # 查询命令固定值
CONTROL_FIXED = 0x00020002  # 控制命令固定值
TIMEOUT = 30  # 通信超时时间(秒)


# 命令构造
def build_query_cmd():
    """构建查询命令"""
    header = struct.pack('B', HEADER_QUERY)  # 明确使用查询标识
    fixed = struct.pack('>I', QUERY_FIXED)  # 大端模式4字节
    return header + fixed


def build_control_cmd(start=True):
    """构建控制命令"""
    header = struct.pack('B', HEADER_QUERY)  # 控制命令使用查询标识
    fixed = struct.pack('>I', CONTROL_FIXED)
    mac = b'\xBC\xEC\xA0\x28\x8D\xA6'  # MAC地址
    channel = b'\x03'  # 通道设置
    signal = b'\x01' if start else b'\x00'  # 启动/停止信号
    return header + fixed + mac + channel + signal


# 网络配置
DEVELOP_BOARD_IP = '************'
#板子的ip地址
DEVELOP_BOARD_PORT = 8080
#端口
LOCAL_PORT = 8080
# 留空表示绑定所有可用网卡，若有问题请替换为实际IP
LOCAL_IP = ''

# 视频参数
VIDEO_WIDTH = 1280
VIDEO_HEIGHT = 720
VIDEO_CHANNELS = 3
FRAME_SIZE = VIDEO_WIDTH * VIDEO_HEIGHT * VIDEO_CHANNELS
BUFFER_SIZE = 65536


def verify_response_header(header_byte):
    """验证应答Header是否正确"""
    return (header_byte & 0x01) == HEADER_RESPONSE


def receive_video_stream():
    # 创建UDP套接字
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    server_socket.settimeout(TIMEOUT)  # 设置全局超时

    # 绑定本地地址
    try:
        server_address = (LOCAL_IP, LOCAL_PORT)
        server_socket.bind(server_address)
        print(f"已绑定本地地址: {server_address[0] if server_address[0] else '所有网卡'}:{server_address[1]}")
    except Exception as e:
        print(f"绑定失败: {e}")
        return

    query_cmd = build_query_cmd()
    control_cmd = build_control_cmd(start=True)
    stop_cmd = build_control_cmd(start=False)

    try:
        # 1. 发送查询命令并等待应答
        print("\n===== 发送查询命令 =====")
        print(f"命令内容: {query_cmd.hex()}")
        # 修正：使用构建的命令而不是硬编码字符串
        server_socket.sendto(query_cmd, (DEVELOP_BOARD_IP, DEVELOP_BOARD_PORT))

        # 接收应答
        print("等待开发板应答...")
        resp_data, addr = server_socket.recvfrom(4096)

        if addr[0] != DEVELOP_BOARD_IP:
            print(f"错误: 收到来自未知地址 {addr} 的应答")
            return

        print(f"收到应答 (长度: {len(resp_data)}): {resp_data.hex()}")

        # 验证应答Header
        if len(resp_data) < 1:
            print("错误: 应答数据为空")
            return

        if not verify_response_header(resp_data[0]):
            print(f"警告: 应答Header不符合预期 (0x{resp_data[0]:02x})")
            print("尝试继续通信...")  # 宽容处理，避免严格校验导致中断

        # 2. 发送控制命令
        print("\n===== 发送控制命令 =====")
        print(f"命令内容: {control_cmd.hex()}")
        server_socket.sendto(control_cmd, (DEVELOP_BOARD_IP, DEVELOP_BOARD_PORT))

        # 3. 接收视频流
        print("\n===== 开始接收视频流 =====")
        frame_count = 0
        start_time = time.time()

        while True:
            try:
                data, addr = server_socket.recvfrom(BUFFER_SIZE)
                frame_count += 1

                # 检查发送方
                if addr[0] != DEVELOP_BOARD_IP:
                    print(f"忽略来自 {addr} 的数据")
                    continue

                # 打印数据信息
                if frame_count % 10 == 0:
                    print(f"收到第{frame_count}帧 (长度: {len(data)})")

                # 处理图像数据 (跳过协议头)
                if len(data) > 8:  # 假设前8字节为协议头
                    img_data = data[8:]
                else:
                    img_data = data
                print(len(data))
                # 转换为图像
                img_array = np.frombuffer(img_data, dtype=np.uint8)

                # 尝试两种格式解析
                try:
                    if img_array.size == FRAME_SIZE:
                        # 原始图像数据
                        frame = img_array.reshape((VIDEO_HEIGHT, VIDEO_WIDTH, VIDEO_CHANNELS))
                    else:
                        # 压缩图像数据
                        frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

                    if frame is not None:
                        cv2.imshow("Zynq Video Stream", frame)

                except Exception as e:
                    print(f"图像处理错误: {e}")

                # 计算帧率
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    fps = 30 / elapsed if elapsed > 0 else 0
                    print(f"帧率: {fps:.2f} FPS")
                    start_time = time.time()

                # 发送保持命令
                if frame_count % 5 == 0:
                    server_socket.sendto(control_cmd, (DEVELOP_BOARD_IP, DEVELOP_BOARD_PORT))

                # 按q退出
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    print("用户请求退出")
                    break

            except socket.timeout:
                print("接收超时，尝试重新发送控制命令")
                server_socket.sendto(control_cmd, (DEVELOP_BOARD_IP, DEVELOP_BOARD_PORT))
            except Exception as e:
                print(f"接收过程错误: {e}")
                break

    except socket.timeout:
        print(f"超时: {TIMEOUT}秒内未收到应答")
    except Exception as e:
        print(f"通信错误: {e}")
    finally:
        # 发送停止命令
        print("\n发送停止命令")
        server_socket.sendto(stop_cmd, (DEVELOP_BOARD_IP, DEVELOP_BOARD_PORT))
        server_socket.close()
        cv2.destroyAllWindows()
        print("程序已退出")

if __name__ == "__main__":
    receive_video_stream()
