#!/usr/bin/env python3
"""
简单的网络连接测试脚本
用于诊断与Zynq开发板的连接问题
"""

import socket
import subprocess
import sys
import time

def test_ping(ip):
    """测试ping连通性"""
    print(f"[测试] Ping {ip}...")
    try:
        result = subprocess.run(['ping', '-n', '1', ip], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"[成功] Ping {ip} 成功")
            return True
        else:
            print(f"[失败] Ping {ip} 失败")
            print(f"输出: {result.stdout}")
            return False
    except Exception as e:
        print(f"[错误] Ping测试异常: {e}")
        return False

def test_udp_send(ip, port):
    """测试UDP发送"""
    print(f"[测试] UDP发送到 {ip}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        
        # 发送测试数据
        test_data = b"HELLO_ZYNQ"
        sock.sendto(test_data, (ip, port))
        print(f"[成功] UDP数据发送成功")
        
        # 尝试接收响应
        try:
            data, addr = sock.recvfrom(1024)
            print(f"[成功] 收到响应: {len(data)} 字节，来源: {addr}")
            print(f"响应内容: {data}")
            return True
        except socket.timeout:
            print(f"[警告] 没有收到响应（超时）")
            return False
            
    except Exception as e:
        print(f"[错误] UDP测试失败: {e}")
        return False
    finally:
        sock.close()

def test_port_binding(port):
    """测试本地端口绑定"""
    print(f"[测试] 绑定本地端口 {port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('', port))
        print(f"[成功] 端口 {port} 绑定成功")
        sock.close()
        return True
    except Exception as e:
        print(f"[失败] 端口 {port} 绑定失败: {e}")
        return False

def main():
    print("========== Zynq开发板连接诊断工具 ==========")
    
    # 从配置文件读取参数
    try:
        import json
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        ip = config['DEVELOP_BOARD_IP']
        port = config['DEVELOP_BOARD_PORT']
        local_port = config['LOCAL_PORT']
        
        print(f"配置信息:")
        print(f"  开发板IP: {ip}")
        print(f"  开发板端口: {port}")
        print(f"  本地端口: {local_port}")
        print()
        
    except Exception as e:
        print(f"[错误] 读取配置文件失败: {e}")
        return
    
    # 执行测试
    tests = [
        ("Ping连通性", lambda: test_ping(ip)),
        ("本地端口绑定", lambda: test_port_binding(local_port)),
        ("UDP通信", lambda: test_udp_send(ip, port))
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)
    
    # 显示测试结果总结
    print(f"\n{'='*50}")
    print("测试结果总结:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    # 给出建议
    print(f"\n建议:")
    if not results[0][1]:  # ping失败
        print("  1. 检查网络连接，确保电脑和开发板在同一网络")
        print("  2. 检查开发板是否已启动")
        print("  3. 确认IP地址是否正确")
    elif not results[1][1]:  # 端口绑定失败
        print("  1. 检查端口是否被其他程序占用")
        print("  2. 尝试使用其他端口")
    elif not results[2][1]:  # UDP通信失败
        print("  1. 检查开发板上的UDP服务是否启动")
        print("  2. 检查防火墙设置")
        print("  3. 确认端口号是否正确")

if __name__ == "__main__":
    main()
