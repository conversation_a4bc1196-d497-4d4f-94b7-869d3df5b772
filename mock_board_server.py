#!/usr/bin/env python3
"""
模拟开发板服务器 - 用于测试UDP视频接收协议
"""

import socket
import struct
import time
import threading
from datetime import datetime

class MockBoardServer:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        
        # 模拟的MAC地址
        self.mac_address = bytes.fromhex('bceca0288da6')
        # 模拟的IP地址 (************)
        self.ip_address = struct.pack('!I', 0xC0A8010A)
        
    def start(self):
        """启动模拟服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.running = True
            
            print(f"[MOCK] 模拟开发板服务器启动在 {self.host}:{self.port}")
            print(f"[MOCK] MAC地址: {self.mac_address.hex()}")
            print(f"[MOCK] IP地址: {socket.inet_ntoa(self.ip_address)}")
            print("[MOCK] 等待查询命令...")
            
            while self.running:
                try:
                    data, addr = self.socket.recvfrom(1024)
                    self.handle_request(data, addr)
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        print(f"[MOCK ERROR] 处理请求时出错: {e}")
                        
        except Exception as e:
            print(f"[MOCK ERROR] 启动服务器失败: {e}")
        finally:
            if self.socket:
                self.socket.close()
                
    def handle_request(self, data, addr):
        """处理收到的请求"""
        print(f"\n[MOCK] 收到来自 {addr} 的数据:")
        print(f"[MOCK] 数据长度: {len(data)} 字节")
        print(f"[MOCK] 数据内容: {data.hex()}")
        
        if len(data) >= 5:
            header = data[0]
            fixed_part = struct.unpack('>I', data[1:5])[0]
            
            print(f"[MOCK] Header: 0x{header:02x}")
            print(f"[MOCK] Fixed: 0x{fixed_part:08x}")
            
            # 检查是否是查询命令
            if header & 0x01 == 0 and fixed_part == 0x00020001:
                print("[MOCK] 识别为查询命令，发送应答...")
                self.send_response(addr)
            elif header & 0x01 == 1 and fixed_part == 0x00020002:
                print("[MOCK] 识别为控制命令")
                if len(data) >= 12:
                    mac = data[5:11]
                    channel = data[11]
                    signal = data[12] if len(data) > 12 else 0
                    print(f"[MOCK] MAC: {mac.hex()}")
                    print(f"[MOCK] Channel: {channel}")
                    print(f"[MOCK] Signal: {signal}")
            else:
                print(f"[MOCK] 未知命令格式")
        else:
            print(f"[MOCK] 数据太短，无法解析")
            
    def send_response(self, addr):
        """发送查询应答"""
        try:
            # 构建应答包：Header(1) + Fixed(4) + MAC(6) + IP(4) + 0x02(1) = 16字节
            response = bytearray()
            response.append(0x01)  # Header: bit0=1表示应答
            response.extend(struct.pack('>I', 0x00020001))  # Fixed part
            response.extend(self.mac_address)  # MAC地址
            response.extend(self.ip_address)   # IP地址
            response.append(0x02)              # 结束标志
            
            print(f"[MOCK] 发送应答到 {addr}")
            print(f"[MOCK] 应答内容: {response.hex()}")
            print(f"[MOCK] 应答长度: {len(response)} 字节")
            
            self.socket.sendto(response, addr)
            print("[MOCK] 应答已发送")
            
        except Exception as e:
            print(f"[MOCK ERROR] 发送应答失败: {e}")
            
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.socket:
            self.socket.close()

def main():
    server = MockBoardServer()
    
    try:
        # 在后台线程启动服务器
        server_thread = threading.Thread(target=server.start)
        server_thread.daemon = True
        server_thread.start()
        
        print("[MOCK] 按 Ctrl+C 停止服务器")
        
        # 主线程等待
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n[MOCK] 收到停止信号，关闭服务器...")
        server.stop()
        print("[MOCK] 服务器已停止")

if __name__ == "__main__":
    main()
