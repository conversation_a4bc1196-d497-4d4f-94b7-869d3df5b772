#!/usr/bin/env python3
"""
Zynq开发板UDP视频服务模拟器
用于测试视频接收程序
"""

import socket
import time
import struct
import threading
import numpy as np
import cv2

class ZynqSimulator:
    def __init__(self, ip="************", port=8080):
        self.ip = ip
        self.port = port
        self.socket = None
        self.running = False
        self.clients = set()
        
        # 协议常量
        self.HEADER_QUERY = 0x00
        self.HEADER_RESPONSE = 0x01
        self.QUERY_FIXED = 0x00020001
        self.CONTROL_FIXED = 0x00020002
        
        print(f"[模拟器] 初始化Zynq模拟器: {ip}:{port}")
    
    def create_test_frame(self, frame_count):
        """创建测试视频帧"""
        # 创建一个彩色测试图像
        height, width = 720, 1280
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            for x in range(width):
                frame[y, x] = [
                    (x * 255) // width,  # 红色渐变
                    (y * 255) // height, # 绿色渐变
                    ((x + y) * 255) // (width + height)  # 蓝色渐变
                ]
        
        # 添加文字信息
        text = f"Frame: {frame_count}"
        cv2.putText(frame, text, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 添加时间戳
        timestamp = time.strftime("%H:%M:%S")
        cv2.putText(frame, timestamp, (50, 120), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 添加移动的圆形
        center_x = int(640 + 200 * np.sin(frame_count * 0.1))
        center_y = int(360 + 100 * np.cos(frame_count * 0.1))
        cv2.circle(frame, (center_x, center_y), 50, (0, 255, 255), -1)
        
        return frame
    
    def handle_query(self, data, addr):
        """处理查询命令"""
        print(f"[模拟器] 收到查询命令，来源: {addr}")
        
        # 发送响应
        response = struct.pack('B', self.HEADER_RESPONSE) + b'\x00\x00\x00\x01'
        self.socket.sendto(response, addr)
        print(f"[模拟器] 发送查询响应到: {addr}")
    
    def handle_control(self, data, addr):
        """处理控制命令"""
        print(f"[模拟器] 收到控制命令，来源: {addr}")
        
        if len(data) >= 12:  # 至少包含header + fixed + mac + channel + signal
            signal = data[-1]
            if signal == 0x01:  # 启动视频流
                print(f"[模拟器] 客户端请求启动视频流: {addr}")
                self.clients.add(addr)
            else:  # 停止视频流
                print(f"[模拟器] 客户端请求停止视频流: {addr}")
                self.clients.discard(addr)
    
    def send_video_frames(self):
        """发送视频帧"""
        frame_count = 0
        
        while self.running:
            if self.clients:
                # 创建测试帧
                frame = self.create_test_frame(frame_count)
                
                # 编码为JPEG
                _, encoded = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                frame_data = encoded.tobytes()
                
                # 添加协议头
                header = struct.pack('B', self.HEADER_RESPONSE)
                header += struct.pack('>I', frame_count)  # 帧序号
                header += b'\x00\x00\x00'  # 填充到8字节
                
                packet = header + frame_data
                
                # 发送给所有客户端
                for client_addr in list(self.clients):
                    try:
                        self.socket.sendto(packet, client_addr)
                    except Exception as e:
                        print(f"[模拟器] 发送失败: {e}")
                        self.clients.discard(client_addr)
                
                if frame_count % 30 == 0:
                    print(f"[模拟器] 已发送 {frame_count} 帧到 {len(self.clients)} 个客户端")
                
                frame_count += 1
            
            time.sleep(1/30)  # 30 FPS
    
    def start(self):
        """启动模拟器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.ip, self.port))
            self.running = True
            
            print(f"[模拟器] 服务启动成功，监听: {self.ip}:{self.port}")
            
            # 启动视频发送线程
            video_thread = threading.Thread(target=self.send_video_frames)
            video_thread.daemon = True
            video_thread.start()
            
            # 主循环处理命令
            while self.running:
                try:
                    data, addr = self.socket.recvfrom(4096)
                    
                    if len(data) >= 5:
                        header = data[0]
                        fixed = struct.unpack('>I', data[1:5])[0]
                        
                        if header == self.HEADER_QUERY and fixed == self.QUERY_FIXED:
                            self.handle_query(data, addr)
                        elif header == self.HEADER_QUERY and fixed == self.CONTROL_FIXED:
                            self.handle_control(data, addr)
                        else:
                            print(f"[模拟器] 未知命令: header={header:02x}, fixed={fixed:08x}")
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"[模拟器] 接收错误: {e}")
                    
        except Exception as e:
            print(f"[模拟器] 启动失败: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止模拟器"""
        print(f"[模拟器] 正在停止...")
        self.running = False
        if self.socket:
            self.socket.close()
        print(f"[模拟器] 已停止")

def main():
    print("========== Zynq开发板模拟器 ==========")
    print("这个模拟器会模拟Zynq开发板的UDP视频服务")
    print("按 Ctrl+C 停止模拟器")
    print()
    
    simulator = ZynqSimulator()
    
    try:
        simulator.start()
    except KeyboardInterrupt:
        print("\n[模拟器] 用户中断")
    finally:
        simulator.stop()

if __name__ == "__main__":
    main()
